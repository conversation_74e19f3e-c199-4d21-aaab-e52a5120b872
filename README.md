# Splitter - A/B Testing Sidecar Service

Высокопроизводительный сервис для A/B тестирования, предназначенный для работы в качестве sidecar контейнера в Kubernetes.

## Особенности

- ✅ **Graceful shutdown** - корректное завершение работы
- ✅ **Health checks** - эндпоинты для проверки состояния
- ✅ **Structured logging** - JSON логи с уровнями
- ✅ **Metrics** - встроенные метрики для мониторинга
- ✅ **Production ready** - таймауты, error handling, recovery
- ✅ **Kubernetes native** - готов для развертывания в K8s
- ✅ **High performance** - оптимизирован для высоких нагрузок (300k+ req/day)

## Архитектура

```
User → LoadBalancer → Ingress → Splitter Sidecar → nginx/app
```

Сервис работает как sidecar контейнер и:
1. Определяет группу пользователя на основе device_id
2. Если пользователь в группе B - редиректит на другой URL
3. Если в группе A - проксирует запрос к основному приложению
4. Асинхронно отправляет аналитику

## Быстрый старт

### Локальная разработка

```bash
# Установка зависимостей
make deps

# Запуск в режиме разработки
make dev

# Или обычный запуск
make run-local
```

### Сборка и развертывание

```bash
# Сборка Docker образа
make docker-build

# Отправка в registry
make docker-push

# Развертывание в Kubernetes
make deploy

# Проверка статуса
make status
```

## Конфигурация

Все настройки задаются через переменные окружения:

| Переменная | Описание | По умолчанию |
|------------|----------|--------------|
| `EXPERIMENT_NAME` | Название эксперимента | `monolit-mf-ab-test` |
| `EXPERIMENT_GROUP_A` | Название группы A | `Monolit` |
| `EXPERIMENT_GROUP_B` | Название группы B | `MF` |
| `SPLIT_PERCENT` | Процент трафика в группу B | `50` |
| `PROXY_URL` | URL для проксирования группы A | `https://ya.ru` |
| `REDIRECT_URL` | URL для редиректа группы B | `https://google.com` |
| `SOGU_URL` | URL для отправки аналитики | - |
| `PORT` | Порт сервиса | `8080` |
| `BROWSER_DEBUG_LOGS` | Включить debug логи | `false` |

## API Endpoints

### Основной трафик
- `GET /` - основной обработчик A/B тестирования

### Мониторинг
- `GET /health` - проверка здоровья сервиса
- `GET /ready` - проверка готовности к работе
- `GET /metrics` - метрики в JSON формате

### Пример ответа /metrics
```json
{
  "total_requests": 1000,
  "group_a_requests": 500,
  "group_b_requests": 500,
  "analytics_errors": 2,
  "proxy_errors": 1,
  "uptime_seconds": 3600
}
```

## Мониторинг и логирование

### Логи
Сервис использует структурированное логирование в JSON формате:

```json
{
  "time": "2024-01-15T10:30:00Z",
  "level": "INFO",
  "msg": "Request processed",
  "method": "GET",
  "path": "/",
  "status": 200,
  "duration_ms": 15,
  "user_agent": "Mozilla/5.0..."
}
```

### Метрики
- Общее количество запросов
- Запросы по группам A и B
- Ошибки аналитики и проксирования
- Время работы сервиса

## Kubernetes развертывание

Сервис развертывается как sidecar контейнер рядом с основным приложением:

```yaml
containers:
- name: nginx          # Основное приложение
  image: nginx:alpine
  ports:
  - containerPort: 80

- name: splitter       # Sidecar для A/B тестирования
  image: your-registry/splitter:latest
  ports:
  - containerPort: 8080
```

## Производительность

Сервис оптимизирован для высоких нагрузок:
- HTTP клиент с пулом соединений
- Асинхронная отправка аналитики
- Минимальные аллокации памяти
- Graceful shutdown без потери запросов

## Разработка

### Требования
- Go 1.24+
- Docker
- Kubernetes (для развертывания)

### Полезные команды
```bash
make help           # Показать все доступные команды
make test           # Запустить тесты
make build          # Собрать бинарник
make docker-build   # Собрать Docker образ
make deploy         # Развернуть в K8s
make logs           # Показать логи
make metrics        # Показать метрики
```

## Безопасность

- Запуск от непривилегированного пользователя
- Минимальный базовый образ (Alpine)
- Валидация всех входных данных
- Таймауты для всех HTTP запросов
