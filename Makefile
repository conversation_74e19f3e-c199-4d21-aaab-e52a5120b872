# Variables
APP_NAME := splitter
DOCKER_REGISTRY := your-registry.com
DOCKER_IMAGE := $(DOCKER_REGISTRY)/$(APP_NAME)
VERSION := $(shell date +%Y%m%d-%H%M%S)
NAMESPACE := default

# Go variables
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod

.PHONY: help build test clean docker-build docker-push deploy undeploy logs

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

build: ## Build the Go application
	$(GOBUILD) -ldflags="-X main.AppVersion=$(VERSION)" -o $(APP_NAME) .

test: ## Run tests
	$(GOTEST) -v ./...

clean: ## Clean build artifacts
	$(GOCLEAN)
	rm -f $(APP_NAME)

deps: ## Download dependencies
	$(GOMOD) download
	$(GOMOD) tidy

docker-build: ## Build Docker image
	docker build -t $(DOCKER_IMAGE):$(VERSION) .
	docker tag $(DOCKER_IMAGE):$(VERSION) $(DOCKER_IMAGE):latest

docker-push: docker-build ## Push Docker image to registry
	docker push $(DOCKER_IMAGE):$(VERSION)
	docker push $(DOCKER_IMAGE):latest

deploy: ## Deploy to Kubernetes
	kubectl apply -f k8s-manifests.yaml -n $(NAMESPACE)

undeploy: ## Remove from Kubernetes
	kubectl delete -f k8s-manifests.yaml -n $(NAMESPACE) --ignore-not-found=true

logs: ## Show application logs
	kubectl logs -f deployment/app-with-splitter -c splitter -n $(NAMESPACE)

status: ## Show deployment status
	kubectl get pods,svc,ingress -l app=app-with-splitter -n $(NAMESPACE)

metrics: ## Show application metrics
	kubectl port-forward svc/app-with-splitter-service 8080:8080 -n $(NAMESPACE) &
	sleep 2
	curl -s http://localhost:8080/metrics | jq .
	pkill -f "kubectl port-forward"

health: ## Check application health
	kubectl port-forward svc/app-with-splitter-service 8080:8080 -n $(NAMESPACE) &
	sleep 2
	curl -s http://localhost:8080/health | jq .
	curl -s http://localhost:8080/ready | jq .
	pkill -f "kubectl port-forward"

run-local: build ## Run application locally
	./$(APP_NAME)

dev: ## Run in development mode with live reload (requires air)
	air

install-dev-tools: ## Install development tools
	go install github.com/cosmtrek/air@latest
