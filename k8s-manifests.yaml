---
apiVersion: v1
kind: ConfigMap
metadata:
  name: splitter-config
  namespace: default
data:
  EXPERIMENT_NAME: "monolit-mf-ab-test"
  EXPERIMENT_GROUP_A: "Monolit"
  EXPERIMENT_GROUP_B: "MF"
  EXPERIMENT_EVENT_HIT: "experiments.hit"
  REDIRECT_URL: "https://google.com"
  PROXY_URL: "http://nginx-service:80"
  SOGU_URL: "https://sogu-staging.sogu.dev.tripster.tech/events/"
  SPLIT_PERCENT: "50"
  BROWSER_DEBUG_LOGS: "false"
  PORT: "8080"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-with-splitter
  namespace: default
  labels:
    app: app-with-splitter
spec:
  replicas: 3
  selector:
    matchLabels:
      app: app-with-splitter
  template:
    metadata:
      labels:
        app: app-with-splitter
    spec:
      containers:
      # Main application container (nginx in this example)
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        
      # Splitter sidecar container
      - name: splitter
        image: your-registry/splitter:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: splitter-config
        resources:
          requests:
            memory: "32Mi"
            cpu: "25m"
          limits:
            memory: "64Mi"
            cpu: "50m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3

---
apiVersion: v1
kind: Service
metadata:
  name: app-with-splitter-service
  namespace: default
  labels:
    app: app-with-splitter
spec:
  selector:
    app: app-with-splitter
  ports:
  - name: splitter
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: app-with-splitter-service
            port:
              number: 8080

---
# ServiceMonitor for Prometheus (optional)
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: splitter-metrics
  namespace: default
  labels:
    app: app-with-splitter
spec:
  selector:
    matchLabels:
      app: app-with-splitter
  endpoints:
  - port: splitter
    path: /metrics
    interval: 30s
